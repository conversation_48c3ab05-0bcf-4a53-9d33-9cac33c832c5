# Dossier Code Processing Tool - Design Document

## Overview

This document outlines the design for a Python Tkinter-based GUI application that processes dossier codes. The tool automatically formats input codes, connects to a MongoDB database to update dossier records, and triggers API notifications upon successful processing.

### Core Functionality
- Accept comma-separated dossier codes via GUI input
- Automatically format and parse the input codes
- Connect to MongoDB database for dossier record updates
- Process appointment and completion date logic
- Send API notifications for successfully processed records

## Technology Stack & Dependencies

### Core Technologies
- **GUI Framework**: Python Tkinter with ttkbootstrap for modern styling
- **Database**: MongoDB (pymongo driver)
- **HTTP Client**: requests library for API calls
- **Date/Time Handling**: datetime, timedelta modules
- **Random Operations**: random module for time calculations

### Required Dependencies
```python
# requirements.txt
ttkbootstrap==1.10.1
pymongo==4.6.0
requests==2.31.0
```

## Architecture

### File Structure
```
tool.py                  # Single-file implementation
config.json             # Configuration file
requirements.txt        # Dependencies
```

### Component Architecture

#### Main Classes
- **DossierProcessorApp**: Main application class
- **DatabaseManager**: MongoDB operations
- **APIClient**: HTTP API communication
- **CodeProcessor**: Core business logic implementation

## GUI Design & Layout

### Window Layout
```
┌──────────────────────────────────────────────┐
│            Dossier Code Processor            │
├──────────────────────────────────────────────┤
│ Codes: [ABC123, SSS456, DEF789        ] [▶] │
│                                              │
│ MongoDB: [mongodb://localhost:27017    ] [●] │
│ API URL: [http://api.example.com/notify] [●] │
│                                              │
│ Progress: [████████████████] 75% (3/4)      │
│                                              │
│ Results:                                     │
│ ┌──────────────────────────────────────────┐ │
│ │ ✓ ABC123 - Updated successfully        │ │
│ │ ✓ SSS456 - Updated successfully        │ │
│ │ ✗ DEF789 - Document not found          │ │
│ └──────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

### UI Component Structure
```mermaid
graph TD
    A[Root Window] --> B[Input Frame]
    A --> C[Config Frame]
    A --> D[Progress Frame]
    A --> E[Results Frame]
    
    B --> B1[Entry Widget]
    B --> B2[Process Button]
    
    C --> C1[MongoDB Entry]
    C --> C2[API Entry]
    
    D --> D1[Progress Bar]
    D --> D2[Status Label]
    
    E --> E1[Text Widget]
    E --> E2[Scrollbar]
```

## Data Models & Database Schema

## Implementation Structure

### Core Function Implementation
```python
def sync_ho_so_tre_han(code, db_client):
    """
    Process dossier code according to business rules
    Returns: 1 (success), -1 (not found), 0 (error)
    """
    db = db_client['svcPadman']
    collection = db['dossier']
    
    try:
        document = collection.find_one({"code": code})
        if not document:
            return -1
            
        if 'appointmentDate' in document:
            # Process existing appointment
            appointment_date = document['appointmentDate']
            if isinstance(appointment_date, str):
                appointment_date = datetime.strptime(
                    appointment_date, '%Y-%m-%dT%H:%M:%S.%fZ'
                )
            
            completed_date = appointment_date - timedelta(
                hours=random.uniform(1, 4)
            )
            
            collection.update_one(
                {"_id": document['_id']}, 
                {"$set": {"completedDate": completed_date}}
            )
        else:
            # Create missing appointment fields
            created_date = document['createdDate']
            appointment_date = created_date + timedelta(days=1)
            
            collection.update_one(
                {"_id": document['_id']}, 
                {"$set": {
                    "acceptedDate": created_date,
                    "appointmentDate": appointment_date,
                    "completedDate": appointment_date
                }}
            )
        
        return 1
    except Exception as e:
        print(f"Error processing {code}: {e}")
        return 0
```

### Input Processing
```python
def parse_dossier_codes(input_text):
    """
    Parse and validate dossier codes from input
    Automatically adds commas between codes if missing
    """
    # Remove extra whitespace and split by common delimiters
    codes = re.split(r'[,\s]+', input_text.strip())
    # Filter out empty strings
    codes = [code.strip() for code in codes if code.strip()]
    return codes
```

### API Notification
```python
def notify_api(dossier_code, api_url):
    """
    Send POST notification to API endpoint
    """
    try:
        response = requests.post(
            api_url,
            json={"dossierCode": dossier_code},
            timeout=10
        )
        return response.status_code == 200
    except Exception as e:
        print(f"API notification failed for {dossier_code}: {e}")
        return False
```

## Business Logic Layer

### Core Processing Flow
```mermaid
flowchart TD
    A[Input Codes] --> B[Parse & Validate Codes]
    B --> C[For Each Code]
    C --> D[Connect to MongoDB]
    D --> E[Find Document by Code]
    E --> F{Document Exists?}
    
    F -->|No| G[Return -1: Not Found]
    F -->|Yes| H{Has appointmentDate?}
    
    H -->|Yes| I[Convert appointmentDate to DateTime]
    I --> J[Calculate completedDate<br/>appointmentDate - random(1-4 hours)]
    J --> K[Update completedDate]
    K --> L[Return 1: Success]
    
    H -->|No| M[Set acceptedDate = createdDate]
    M --> N[Set appointmentDate = createdDate + 1 day]
    N --> O[Set completedDate = appointmentDate]
    O --> P[Return 1: Success]
    
    L --> Q{Processing Successful?}
    P --> Q
    G --> Q
    
    Q -->|Yes| R[Call POST API with code]
    Q -->|No| S[Log Error]
    
    R --> T[Next Code]
    S --> T
    T --> U{More Codes?}
    U -->|Yes| C
    U -->|No| V[Complete Processing]
```

### Date Processing Logic
1. **Existing appointmentDate**:
   - Convert string to datetime if needed
   - Calculate completedDate = appointmentDate - random(1-4 hours)
   - Update document with new completedDate

2. **Missing appointmentDate**:
   - Set acceptedDate = createdDate
   - Set appointmentDate = createdDate + 1 day
   - Set completedDate = appointmentDate
   - Update document with all new fields

### Error Handling Strategy
- **Database Connection Errors**: Retry with exponential backoff
- **Document Not Found**: Log and continue processing
- **API Call Failures**: Log error but don't halt processing
- **Invalid Input**: Validate and sanitize before processing

## API Integration Layer

### API Service Design
```python
class APIService:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
    
    def notify_success(self, dossier_code: str) -> bool:
        """Send POST request to notify successful processing"""
        pass
    
    def configure_headers(self, headers: dict):
        """Configure default headers for API calls"""
        pass
```

### API Request Format
```json
{
  "method": "POST",
  "endpoint": "/api/dossier/notify",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer <token>"
  },
  "body": {
    "dossierCode": "ABC123",
    "status": "completed",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

## Configuration Management

### Configuration Format
```json
{
  "database": {
    "uri": "mongodb://localhost:27017",
    "database_name": "svcPadman",
    "collection_name": "dossier"
  },
  "api": {
    "endpoint": "http://api.example.com/notify",
    "timeout": 10
  },
  "ui": {
    "theme": "superhero",
    "window_size": "600x400"
  }
}
```

## User Interface Styling

### Main Application Class
```python
class DossierProcessorApp:
    def __init__(self):
        self.root = ttk.Window(themename="superhero")
        self.setup_ui()
        self.mongo_client = None
        self.processing = False
    
    def setup_ui(self):
        # Create main window layout
        pass
    
    def process_codes(self):
        # Main processing logic
        pass
    
    def update_progress(self, current, total):
        # Update progress bar
        pass
```

## Testing Strategy

## Error Handling & Validation

### Input Validation
- Remove empty codes from input
- Validate code format (alphanumeric)
- Handle various input delimiters (comma, space, newline)

### Database Error Handling
- Connection timeout handling
- Retry logic for transient failures
- Graceful degradation when database unavailable

### API Error Handling
- HTTP timeout configuration
- Non-blocking API calls
- Continue processing even if API fails