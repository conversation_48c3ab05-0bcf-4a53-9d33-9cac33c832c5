#!/usr/bin/env python3
"""
Dossier Code Processing Tool
A Python Tkinter-based GUI application that processes dossier codes,
connects to MongoDB database, and triggers API notifications.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import json
import re
import random
from datetime import datetime, timed<PERSON>ta
from typing import List, Tuple, Optional, Dict
import os
import logging
from copy import deepcopy

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("Warning: pandas not available, Excel file functionality disabled")

# HARDCODED CONFIGURATION - These values are not loaded from config.json
HARDCODED_DATABASE_URI = "********************************************************************************************************************************"
HARDCODED_DATABASE_NAME = "svcPadman"
HARDCODED_COLLECTION_NAME = "dossier"
HARDCODED_AUTH_ENDPOINT = "https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token"
HARDCODED_NOTIFY_ENDPOINT = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"

try:
    import ttkbootstrap as ttk_bootstrap
    from ttkbootstrap.constants import *
    BOOTSTRAP_AVAILABLE = True
except ImportError:
    BOOTSTRAP_AVAILABLE = False
    print("Warning: ttkbootstrap not available, using standard tkinter")

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
    from bson import ObjectId
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    print("Warning: pymongo not available, database functionality disabled")

try:
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    import ssl
    # Suppress SSL warnings when verification is disabled
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Warning: requests not available, API functionality disabled")


class ConfigManager:
    """Manages application configuration"""

    DEFAULT_CONFIG = {
        "database": {
            "uri": "HARDCODED - See source code",
            "database_name": "HARDCODED - See source code",
            "collection_name": "HARDCODED - See source code"
        },
        "api": {
            "auth_endpoint": "HARDCODED - See source code",
            "notify_endpoint": "HARDCODED - See source code",
            "timeout": 10
        },
        "ui": {
            "theme": "superhero" if BOOTSTRAP_AVAILABLE else "default",
            "window_size": "700x500"
        },
        "credentials": {
            "username": "",
            "password": "",
            "remember": False,
            "access_token": "",
            "token_expires_in": 0,
            "token_acquired_at": ""
        }
    }

    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self) -> dict:
        """Load configuration from file or create default"""
        print(f"[DEBUG] Loading configuration from: {self.config_file}")
        if os.path.exists(self.config_file):
            try:
                print(f"[DEBUG] Config file exists, reading...")
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                merged_config = {**self.DEFAULT_CONFIG, **loaded_config}
                print(f"[DEBUG] Configuration loaded successfully")
                print(f"[DEBUG] Database URI: HARDCODED in source code")
                print(f"[DEBUG] API Auth endpoint: HARDCODED in source code")
                return merged_config
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"[DEBUG] Warning: Could not load {self.config_file}: {e}, using defaults")
        else:
            print(f"[DEBUG] Config file does not exist, using default configuration")

        print(f"[DEBUG] Using default database URI: {self.DEFAULT_CONFIG['database']['uri']}")
        return self.DEFAULT_CONFIG.copy()

    def save_config(self):
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save config: {e}")

    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def set(self, key_path: str, value):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value


class DatabaseManager:
    """Handles MongoDB database operations"""

    def __init__(self, uri: str, db_name: str, collection_name: str):
        self.uri = uri
        self.db_name = db_name
        self.collection_name = collection_name
        self.client = None
        self.db = None
        self.collection = None

    def connect(self) -> bool:
        """Establish database connection"""
        if not PYMONGO_AVAILABLE:
            print("[DEBUG] pymongo not available, skipping database connection")
            return False

        try:
            print(f"[DEBUG] Attempting to connect to MongoDB: {self.uri}")
            print(f"[DEBUG] Database: {self.db_name}, Collection: {self.collection_name}")

            self.client = MongoClient(self.uri, serverSelectionTimeoutMS=5000)

            # Test connection
            print("[DEBUG] Testing connection with ping command...")
            self.client.admin.command('ping')
            print("[DEBUG] Ping successful")

            self.db = self.client[self.db_name]
            self.collection = self.db["dossier"]

            # Test collection access
            try:
                doc_count = self.collection.estimated_document_count()
                print(f"[DEBUG] Collection accessible with approximately {doc_count} documents")
            except Exception as e:
                print(f"[DEBUG] Warning: Could not get document count: {e}")

            print("[DEBUG] Database connection established successfully")
            return True
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            print(f"[DEBUG] Database connection failed: {e}")
            print(f"[DEBUG] Connection details - URI: {self.uri}")
            return False
        except Exception as e:
            print(f"[DEBUG] Unexpected error during database connection: {e}")
            return False

    def disconnect(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            self.client = None

    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self.client is not None

    def sync_ho_so_tre_han(self, code: str) -> int:
        """
        Process dossier code according to business rules
        Returns: 1 (success), -1 (not found), 0 (error)
        """
        if not self.is_connected():
            print(f"[DEBUG] Cannot process {code}: database not connected")
            return 0

        try:
            print(f"[DEBUG] Processing dossier code: {code}")
            document = self.collection.find_one({"code": code})
            if not document:
                print(f"[DEBUG] Document not found for code: {code}")
                return -1

            print(f"[DEBUG] Found document for {code}, checking appointmentDate...")

            # New logic to check dates before standard processing
            appointment_date_str = document.get('appointmentDate')
            returned_date_str = document.get('returnedDate')
            completed_date_str = document.get('completedDate')

            # Helper to parse dates safely
            def parse_date(date_str):
                if not date_str:
                    return None
                if isinstance(date_str, datetime):
                    return date_str
                try:
                    return datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S.%fZ')
                except (ValueError, TypeError):
                    try:
                        return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    except (ValueError, TypeError):
                        return None

            appointment_date = parse_date(appointment_date_str)
            returned_date = parse_date(returned_date_str)
            completed_date = parse_date(completed_date_str)

            print(f"[DEBUG] Parsed Dates for {code} -> Appointment: {appointment_date}, Returned: {returned_date}, Completed: {completed_date}")

            update_needed = False
            if appointment_date and returned_date:
                is_late = returned_date > appointment_date
                print(f"[DEBUG] Check ReturnedDate for {code}: {returned_date} > {appointment_date} -> {is_late}")
                if is_late:
                    update_needed = True

            if not update_needed and appointment_date and completed_date:
                is_late = completed_date > appointment_date
                print(f"[DEBUG] Check CompletedDate for {code}: {completed_date} > {appointment_date} -> {is_late}")
                if is_late:
                    update_needed = True

            print(f"[DEBUG] Final decision for {code}: update_needed = {update_needed}")

            if update_needed:
                print(f"[DEBUG] Updating appointmentDate for {code} due to completion/return date being later.")
                # Determine the latest date between returned and completed
                latest_date = max(d for d in [returned_date, completed_date] if d is not None)

                # Calculate new appointment date
                new_appointment_date = latest_date + timedelta(days=1)

                # Adjust if it's a weekend
                if new_appointment_date.weekday() == 5:  # Saturday
                    new_appointment_date += timedelta(days=2)
                elif new_appointment_date.weekday() == 6:  # Sunday
                    new_appointment_date += timedelta(days=1)

                update_data = {
                    "appointmentDateBackup": document.get('appointmentDate'),
                    "appointmentDate": new_appointment_date
                }

                print(f"[DEBUG] Preparing to update {code} with data: {update_data}")
                result = self.collection.update_one(
                    {"_id": document['_id']},
                    {"$set": update_data}
                )
                print(f"[DEBUG] Update result for {code}: matched={result.matched_count}, modified={result.modified_count}")

            # Original logic starts here
            elif 'appointmentDate' in document:
                print(f"[DEBUG] Document has appointmentDate, processing existing appointment...")
                # Process existing appointment
                appointment_date = document['appointmentDate']
                if isinstance(appointment_date, str):
                    try:
                        appointment_date = datetime.strptime(
                            appointment_date, '%Y-%m-%dT%H:%M:%S.%fZ'
                        )
                    except ValueError:
                        # Try alternative format
                        appointment_date = datetime.fromisoformat(
                            appointment_date.replace('Z', '+00:00')
                        )

                # Calculate completed date (1-4 hours before appointment)
                completed_date = appointment_date - timedelta(
                    hours=random.uniform(1, 4)
                )

                print(f"[DEBUG] Updating {code} with completedDate: {completed_date}")
                result = self.collection.update_one(
                    {"_id": document['_id']},
                    {"$set": {"completedDate": completed_date}}
                )
                print(f"[DEBUG] Update result for {code}: matched={result.matched_count}, modified={result.modified_count}")
            else:
                print(f"[DEBUG] Document missing appointmentDate, creating appointment fields...")
                # Create missing appointment fields
                created_date = document.get('createdDate')
                if not created_date:
                    created_date = datetime.now()
                    print(f"[DEBUG] No createdDate found, using current time: {created_date}")
                elif isinstance(created_date, str):
                    try:
                        created_date = datetime.strptime(
                            created_date, '%Y-%m-%dT%H:%M:%S.%fZ'
                        )
                    except ValueError:
                        created_date = datetime.fromisoformat(
                            created_date.replace('Z', '+00:00')
                        )
                    print(f"[DEBUG] Parsed createdDate: {created_date}")

                appointment_date = created_date + timedelta(days=1)
                print(f"[DEBUG] Setting appointmentDate to: {appointment_date}")

                update_data = {
                    "acceptedDate": created_date,
                    "appointmentDate": appointment_date,
                    "completedDate": appointment_date
                }
                print(f"[DEBUG] Updating {code} with new appointment data: {update_data}")

                result = self.collection.update_one(
                    {"_id": document['_id']},
                    {"$set": update_data}
                )
                print(f"[DEBUG] Update result for {code}: matched={result.matched_count}, modified={result.modified_count}")

            print(f"[DEBUG] Successfully processed {code}")
            return 1
        except Exception as e:
            print(f"[DEBUG] Error processing {code}: {e}")
            import traceback
            print(f"[DEBUG] Full traceback: {traceback.format_exc()}")
            return 0

    def read_excel_codes(self, file_path: str) -> List[str]:
        """Read codes from Excel file"""
        if not PANDAS_AVAILABLE:
            print("[DEBUG] pandas not available, cannot read Excel files")
            return []

        try:
            print(f"[DEBUG] Reading Excel file: {file_path}")
            df = pd.read_excel(file_path)
            codes = df['code'].tolist()
            print(f"[DEBUG] Found {len(codes)} codes in Excel file")
            return codes
        except Exception as e:
            print(f"[DEBUG] Error reading Excel file: {e}")
            return []

    def find_dossier_ids(self, codes: List[str]) -> Dict[str, str]:
        """Find dossier IDs by codes"""
        if not self.is_connected():
            print("[DEBUG] Cannot find dossier IDs: database not connected")
            return {}

        dossier_map = {}
        for code in codes:
            try:
                dossier = self.collection.find_one({'code': code})
                if dossier:
                    dossier_map[code] = str(dossier['_id'])
                    print(f"[DEBUG] Found dossier ID for {code}: {dossier['_id']}")
                else:
                    print(f"[DEBUG] No dossier found for code: {code}")
            except Exception as e:
                print(f"[DEBUG] Error finding dossier for code {code}: {e}")

        return dossier_map

    def check_existing_reused_file(self, dossier_id: str) -> bool:
        """Check if dossier already has file with reused=1"""
        if not self.is_connected():
            return False

        try:
            dossier_form_file_collection = self.db['dossierFormFile']
            existing = dossier_form_file_collection.find_one({
                "dossier.id": ObjectId(dossier_id),
                "file.reused": 1
            })
            return existing is not None
        except Exception as e:
            print(f"[DEBUG] Error checking existing file for dossier {dossier_id}: {e}")
            return False

    def get_template_document(self) -> dict:
        """Get a working template document to clone from"""
        if not self.is_connected():
            return None

        try:
            dossier_form_file_collection = self.db['dossierFormFile']
            template = dossier_form_file_collection.find_one({"file.reused": 1})
            if template:
                print("[DEBUG] Found template document for cloning")
                return template
            print("[DEBUG] No template document found")
            return None
        except Exception as e:
            print(f"[DEBUG] Error getting template document: {e}")
            return None

    def clone_and_update_file(self, dossier_id: str, template_doc: dict) -> bool:
        """Clone template document and update with new dossier ID"""
        if not self.is_connected():
            return False

        try:
            dossier_form_file_collection = self.db['dossierFormFile']
            new_doc = deepcopy(template_doc)
            new_doc.pop('_id', None)
            new_doc['dossier']['id'] = ObjectId(dossier_id)
            result = dossier_form_file_collection.insert_one(new_doc)
            print(f"[DEBUG] Cloned document for dossier {dossier_id}: {result.inserted_id}")
            return result.inserted_id is not None
        except Exception as e:
            print(f"[DEBUG] Error cloning document for dossier {dossier_id}: {e}")
            return False

    def update_tphs_file(self, dossier_id: str, template_doc: dict = None) -> Dict:
        """Update file reused flag for a given dossier (TPHS functionality)"""
        result = {
            'success': False,
            'message': '',
            'action': 'none'
        }

        if not self.is_connected():
            result['message'] = 'Database not connected'
            return result

        try:
            if self.check_existing_reused_file(dossier_id):
                result['success'] = True
                result['message'] = "Already has reused file"
                result['action'] = 'skip'
                return result

            dossier_form_file_collection = self.db['dossierFormFile']

            update_result = dossier_form_file_collection.update_many(
                {"dossier.id": ObjectId(dossier_id)},
                {"$set": {"file.$[].reused": 1}}
            )

            if update_result.modified_count > 0:
                result['success'] = True
                result['message'] = f"Updated {update_result.modified_count} documents"
                result['action'] = 'update'
                return result

            if template_doc:
                if self.clone_and_update_file(dossier_id, template_doc):
                    result['success'] = True
                    result['message'] = "Created new document from template"
                    result['action'] = 'clone'
                else:
                    result['message'] = "Failed to clone document"
            else:
                result['message'] = "No documents found to update and no template available"

        except Exception as e:
            result['message'] = str(e)
            print(f"[DEBUG] Error in update_tphs_file: {e}")

        return result


class APIClient:
    """Handles API communication"""

    def __init__(self, auth_url: str, notify_url: str, timeout: int = 10, config_manager=None):
        self.auth_url = auth_url
        self.notify_url = notify_url
        self.timeout = timeout
        self.config = config_manager
        self.session = None
        self.access_token = None
        self.token_expires_in = None
        self.token_acquired_at = None
        if REQUESTS_AVAILABLE:
            self.session = requests.Session()
            self._configure_ssl_session()

        # Load saved token if available
        self.load_saved_token()

    def _configure_ssl_session(self):
        """Configure SSL settings to handle DH_KEY_TOO_SMALL error"""
        try:
            # Create custom SSL context
            ssl_context = ssl.create_default_context()
            ssl_context.set_ciphers('HIGH:!DH:!aNULL')
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # Create custom HTTPAdapter with SSL context
            class SSLAdapter(HTTPAdapter):
                def init_poolmanager(self, *args, **kwargs):
                    kwargs['ssl_context'] = ssl_context
                    return super().init_poolmanager(*args, **kwargs)

            # Mount the adapter
            self.session.mount('https://', SSLAdapter())

            # Set retry strategy
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )

            adapter = HTTPAdapter(max_retries=retry_strategy)
            self.session.mount('http://', adapter)

            print("SSL context configured successfully")
        except Exception as e:
            print(f"Warning: Could not configure SSL context: {e}")

    def load_saved_token(self):
        """Load saved token from config if available and valid"""
        if not self.config:
            return

        try:
            saved_token = self.config.get('credentials.access_token', '')
            saved_expires_in = self.config.get('credentials.token_expires_in', 0)
            saved_acquired_at = self.config.get('credentials.token_acquired_at', '')

            if saved_token and saved_acquired_at:
                # Parse the saved timestamp
                self.token_acquired_at = datetime.fromisoformat(saved_acquired_at)
                self.access_token = saved_token
                self.token_expires_in = saved_expires_in

                # Check if token is still valid
                if self.is_token_valid():
                    # Update session headers with saved token
                    if self.session:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.access_token}'
                        })
                else:
                    # Token expired, clear it
                    self.clear_saved_token()
        except Exception as e:
            print(f"Error loading saved token: {e}")
            self.clear_saved_token()

    def save_token_to_config(self):
        """Save current token to config for persistence"""
        if not self.config or not self.access_token:
            return

        try:
            self.config.set('credentials.access_token', self.access_token)
            self.config.set('credentials.token_expires_in', self.token_expires_in or 0)
            self.config.set('credentials.token_acquired_at',
                          self.token_acquired_at.isoformat() if self.token_acquired_at else '')
            self.config.save_config()
        except Exception as e:
            print(f"Error saving token to config: {e}")

    def clear_saved_token(self):
        """Clear saved token from config"""
        if not self.config:
            return

        try:
            self.config.set('credentials.access_token', '')
            self.config.set('credentials.token_expires_in', 0)
            self.config.set('credentials.token_acquired_at', '')
            self.config.save_config()
        except Exception as e:
            print(f"Error clearing saved token: {e}")

    def get_bearer_token(self, username: str = "admin", password: str = "admin") -> bool:
        """Get bearer token from auth API using form-encoded data"""
        if not REQUESTS_AVAILABLE or not self.session:
            print("[DEBUG] Requests not available or session not initialized")
            return False

        try:
            print(f"[DEBUG] Attempting to get bearer token for user: {username}")
            print(f"[DEBUG] Auth endpoint: {self.auth_url}")

            # Prepare form data as per the provided pattern
            form_data = {
                "grant_type": "password",
                "username": username,
                "password": password,
                "client_id": "web-onegate"
            }
            print(f"[DEBUG] Form data prepared (password hidden): {dict(form_data, password='***')}")

            # Make request with SSL verification disabled and custom headers
            print("[DEBUG] Making POST request to auth endpoint...")
            response = self.session.post(
                self.auth_url,
                data=form_data,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                },
                verify=False,  # Disable SSL verification
                timeout=self.timeout
            )

            print(f"[DEBUG] Response status code: {response.status_code}")
            print(f"[DEBUG] Response headers: {dict(response.headers)}")

            if response.status_code == 200:
                print("[DEBUG] Authentication successful, parsing response...")
                data = response.json()
                self.access_token = data.get('access_token')
                self.token_expires_in = data.get('expires_in')
                self.token_acquired_at = datetime.now()

                print(f"[DEBUG] Token expires in: {self.token_expires_in} seconds")
                print(f"[DEBUG] Token acquired at: {self.token_acquired_at}")

                if self.access_token:
                    print(f"[DEBUG] Access token received (first 50 chars): {self.access_token[:50]}...")
                    # Update session headers with new token
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.access_token}'
                    })

                    # Save token to config for persistence
                    print("[DEBUG] Saving token to config...")
                    self.save_token_to_config()
                    return True
                else:
                    print("[DEBUG] No access token in response")

            print(f"[DEBUG] Token request failed with status: {response.status_code}")
            if response.status_code != 200:
                response_text = response.text[:500] if response.text else "No response content"
                print(f"[DEBUG] Response content: {response_text}")
            return False
        except requests.exceptions.SSLError as e:
            print(f"[DEBUG] SSL Error during token request: {e}")
            print("[DEBUG] Tip: The server may have weak SSL configuration. Consider contacting the API provider.")
            return False
        except requests.exceptions.ConnectionError as e:
            print(f"[DEBUG] Connection error during token request: {e}")
            return False
        except Exception as e:
            print(f"[DEBUG] Failed to get bearer token: {e}")
            import traceback
            print(f"[DEBUG] Full traceback: {traceback.format_exc()}")
            return False

    def is_token_valid(self) -> bool:
        """Check if current token is still valid"""
        if not self.access_token or not self.token_acquired_at or not self.token_expires_in:
            return False

        # Check if token has expired (with 60 second buffer)
        elapsed = (datetime.now() - self.token_acquired_at).total_seconds()
        return elapsed < (self.token_expires_in - 60)

    def ensure_valid_token(self) -> bool:
        """Ensure we have a valid token - returns False if token invalid and can't refresh"""
        if self.is_token_valid():
            return True

        # Token is expired or doesn't exist
        # Clear invalid token and return False - caller must handle re-authentication
        self.clear_saved_token()
        self.access_token = None
        self.token_expires_in = None
        self.token_acquired_at = None
        if self.session:
            self.session.headers.pop('Authorization', None)

        return False

    def notify_success(self, dossier_code: str) -> bool:
        """Send POST notification for successful processing"""
        if not REQUESTS_AVAILABLE or not self.session:
            print(f"[DEBUG] Cannot notify for {dossier_code}: requests not available or session not initialized")
            return False

        # Check if we have a valid token
        if not self.ensure_valid_token():
            print(f"[DEBUG] No valid token available for notification of {dossier_code}")
            return False

        try:
            print(f"[DEBUG] Sending notification for dossier code: {dossier_code}")
            print(f"[DEBUG] Notification endpoint: {self.notify_url}")

            # Send array of dossier codes as per the API pattern
            payload = [dossier_code]
            print(f"[DEBUG] Payload: {payload}")

            headers = {
                "Content-Type": "application/json"
            }
            if 'Authorization' in self.session.headers:
                auth_header = self.session.headers['Authorization']
                print(f"[DEBUG] Using authorization header: {auth_header[:50]}...")

            print("[DEBUG] Making POST request to notification endpoint...")
            response = self.session.post(
                self.notify_url,
                json=payload,  # Send as JSON array
                headers=headers,
                verify=False,  # Disable SSL verification
                timeout=self.timeout
            )

            print(f"[DEBUG] Notification response status: {response.status_code}")
            print(f"[DEBUG] Response headers: {dict(response.headers)}")

            # If unauthorized, token might be expired
            if response.status_code == 401:
                print(f"[DEBUG] API returned 401 for {dossier_code} - token expired or invalid")
                self.clear_saved_token()
                return False

            # Check if request was successful
            if response.status_code == 200:
                try:
                    result_data = response.json()
                    print(f"[DEBUG] Notification response data: {result_data}")
                    # Check affectedRows as per the API pattern
                    if isinstance(result_data, dict) and result_data.get('affectedRows') == 1:
                        print(f"[DEBUG] Notification successful for {dossier_code}")
                        return True
                    else:
                        print(f"[DEBUG] API returned unexpected data for {dossier_code}: {result_data}")
                        return False
                except (ValueError, KeyError) as e:
                    print(f"[DEBUG] Failed to parse API response for {dossier_code}: {e}")
                    print(f"[DEBUG] Raw response: {response.text[:500]}")
                    return False
            else:
                print(f"[DEBUG] API request failed for {dossier_code}: {response.status_code}")
                print(f"[DEBUG] Response content: {response.text[:500]}")
                return False

        except Exception as e:
            print(f"[DEBUG] API notification failed for {dossier_code}: {e}")
            import traceback
            print(f"[DEBUG] Full traceback: {traceback.format_exc()}")
            return False

    def notify_batch_success(self, dossier_codes: List[str]) -> dict:
        """Send POST notification for multiple successful processing"""
        if not REQUESTS_AVAILABLE or not self.session or not dossier_codes:
            return {}

        # Ensure we have a valid token
        if not self.ensure_valid_token():
            return {}

        try:
            # Send array of dossier codes
            payload = dossier_codes

            response = self.session.post(
                self.notify_url,
                json=payload,
                headers={
                    "Content-Type": "application/json"
                },
                timeout=self.timeout
            )

            # If unauthorized, try to refresh token once
            if response.status_code == 401:
                if self.get_bearer_token():
                    response = self.session.post(
                        self.notify_url,
                        json=payload,
                        headers={
                            "Content-Type": "application/json"
                        },
                        timeout=self.timeout
                    )

            # Check if request was successful
            if response.status_code == 200:
                try:
                    result_data = response.json()
                    if isinstance(result_data, dict):
                        affected_rows = result_data.get('affectedRows', 0)
                        return {
                            'success': affected_rows > 0,
                            'affected_rows': affected_rows,
                            'total_sent': len(dossier_codes),
                            'data': result_data
                        }
                    else:
                        return {'success': False, 'error': 'Invalid response format'}
                except (ValueError, KeyError) as e:
                    return {'success': False, 'error': f'Failed to parse response: {e}'}
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def fetch_dossier_by_code(self, dossier_code: str) -> dict:
        """Fetch dossier information by code"""
        if not REQUESTS_AVAILABLE or not self.session:
            return {'success': False, 'error': 'Requests not available'}

        if not self.ensure_valid_token():
            return {'success': False, 'error': 'No valid token'}

        try:
            # API base URL - extract from notify URL
            api_base = self.notify_url.split('/pa/')[0] if '/pa/' in self.notify_url else 'https://ketnoi.dichvucongcamau.gov.vn'
            dossier_url = f"{api_base}/pa/dossier/{dossier_code}/--by-code"

            print(f"[DEBUG] Fetching dossier by code: {dossier_url}")

            response = self.session.get(
                dossier_url,
                headers={'Authorization': f'Bearer {self.access_token}'},
                verify=False,
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('id'):
                    return {'success': True, 'data': data}
                else:
                    return {'success': False, 'error': f'Dossier information not found for {dossier_code}'}
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def fetch_dossier_details(self, dossier_id: str) -> dict:
        """Fetch detailed dossier information by ID"""
        if not REQUESTS_AVAILABLE or not self.session:
            return {'success': False, 'error': 'Requests not available'}

        if not self.ensure_valid_token():
            return {'success': False, 'error': 'No valid token'}

        try:
            # API base URL - extract from notify URL
            api_base = self.notify_url.split('/pa/')[0] if '/pa/' in self.notify_url else 'https://ketnoi.dichvucongcamau.gov.vn'
            detail_url = f"{api_base}/pa/dossier/{dossier_id}/--online"

            print(f"[DEBUG] Fetching dossier details: {detail_url}")

            response = self.session.get(
                detail_url,
                headers={'Authorization': f'Bearer {self.access_token}'},
                verify=False,
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()
                return {'success': True, 'data': data}
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def update_dossier_attachments(self, dossier_id: str, attachments: list) -> dict:
        """Update dossier attachments"""
        if not REQUESTS_AVAILABLE or not self.session:
            return {'success': False, 'error': 'Requests not available'}

        if not self.ensure_valid_token():
            return {'success': False, 'error': 'No valid token'}

        try:
            # API base URL - extract from notify URL
            api_base = self.notify_url.split('/pa/')[0] if '/pa/' in self.notify_url else 'https://ketnoi.dichvucongcamau.gov.vn'
            update_url = f"{api_base}/pa/dossier/{dossier_id}/--online"

            data = {
                "attachment": attachments
            }

            print(f"[DEBUG] Updating dossier attachments: {update_url}")
            print(f"[DEBUG] Attachment data: {data}")

            response = self.session.put(
                update_url,
                json=data,
                headers={
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                },
                verify=False,
                timeout=self.timeout
            )

            if response.status_code == 200:
                return {'success': True, 'data': response.json()}
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text[:200]}'}

        except Exception as e:
            return {'success': False, 'error': str(e)}


class CodeProcessor:
    """Core business logic for processing dossier codes"""

    @staticmethod
    def parse_dossier_codes(input_text: str) -> List[str]:
        """
        Parse and validate dossier codes from input
        Automatically handles various delimiters
        """
        if not input_text:
            return []

        # Remove extra whitespace and split by common delimiters
        codes = re.split(r'[,\s\n\r]+', input_text.strip())
        # Filter out empty strings and validate format
        codes = [code.strip() for code in codes if code.strip()]
        return codes

    @staticmethod
    def validate_code(code: str) -> bool:
        """Validate dossier code format"""
        # Enhanced validation - alphanumeric with possible dashes/underscores/periods
        # Allows formats like: H12.113-250818-0004, ABC-123, DEF_456, etc.
        is_valid = bool(re.match(r'^[A-Za-z0-9._-]+$', code)) and len(code) >= 3
        print(f"[DEBUG] Validating code '{code}': {'VALID' if is_valid else 'INVALID'}")
        if not is_valid:
            print(f"[DEBUG] Validation failed - Length: {len(code)}, Pattern match: {bool(re.match(r'^[A-Za-z0-9._-]+$', code))}")
        return is_valid


class LoginWindow:
    """Separate login window that appears before main application"""

    def __init__(self, config_manager, on_success_callback):
        self.config = config_manager
        self.on_success_callback = on_success_callback
        self.login_successful = False
        self.username = ""
        self.password = ""

        # Create login window
        if BOOTSTRAP_AVAILABLE:
            self.window = ttk_bootstrap.Window(
                themename=self.config.get('ui.theme', 'superhero')
            )
        else:
            self.window = tk.Tk()

        self.window.title("Đăng nhập - Công cụ xử lý mã hồ sơ")
        self.window.geometry("400x400")
        self.window.resizable(False, False)

        # Center the window
        self.center_window()

        # Setup UI
        self.setup_login_ui()

        # Load saved credentials
        self.load_saved_credentials()

        print(f"[DEBUG] LoginWindow using hardcoded auth endpoint: {HARDCODED_AUTH_ENDPOINT}")

        # Initialize API client for login with hardcoded endpoints
        self.api_client = APIClient(
            HARDCODED_AUTH_ENDPOINT,
            HARDCODED_NOTIFY_ENDPOINT,
            10,  # timeout
            self.config  # Pass config manager for token persistence only
        )

    def center_window(self):
        """Center the login window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def setup_login_ui(self):
        """Setup login window UI"""
        # Main container
        main_frame = ttk.Frame(self.window, padding="30")
        main_frame.pack(fill="both", expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="ĐĂNG NHẬP HỆ THỐNG",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 30))

        # Login form frame
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill="x", pady=(0, 20))

        # Username
        ttk.Label(form_frame, text="Tên đăng nhập:").pack(anchor="w", pady=(0, 5))
        self.username_entry = ttk.Entry(form_frame, font=('Arial', 11))
        self.username_entry.pack(fill="x", pady=(0, 15))

        # Password
        ttk.Label(form_frame, text="Mật khẩu:").pack(anchor="w", pady=(0, 5))
        self.password_entry = ttk.Entry(form_frame, show="*", font=('Arial', 11))
        self.password_entry.pack(fill="x", pady=(0, 15))

        # Remember checkbox
        self.remember_var = tk.BooleanVar()
        self.remember_checkbox = ttk.Checkbutton(
            form_frame,
            text="Ghi nhớ tài khoản",
            variable=self.remember_var
        )
        self.remember_checkbox.pack(anchor="w", pady=(0, 20))

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x")

        # Login button
        self.login_btn = ttk.Button(
            buttons_frame,
            text="Đăng nhập",
            command=self.handle_login,
            style="success.TButton" if BOOTSTRAP_AVAILABLE else None
        )
        self.login_btn.pack(side="right", padx=(10, 0))

        # Exit button
        exit_btn = ttk.Button(
            buttons_frame,
            text="Thoát",
            command=self.window.quit,
            style="secondary.TButton" if BOOTSTRAP_AVAILABLE else None
        )
        exit_btn.pack(side="right")

        # Status label
        self.status_label = ttk.Label(main_frame, text="", foreground="red")
        self.status_label.pack(pady=(10, 0))

        # Bind Enter key to login
        self.window.bind('<Return>', lambda e: self.handle_login())

        # Focus on username entry
        self.username_entry.focus()

    def load_saved_credentials(self):
        """Load saved credentials if remember option was checked"""
        try:
            saved_username = self.config.get('credentials.username', '')
            saved_password = self.config.get('credentials.password', '')
            remember = self.config.get('credentials.remember', False)

            if remember and saved_username:
                self.username_entry.insert(0, saved_username)
                if saved_password:
                    self.password_entry.insert(0, saved_password)
                self.remember_var.set(True)
                # Focus on password if username is pre-filled
                if saved_username:
                    self.password_entry.focus()
        except Exception as e:
            print(f"Error loading credentials: {e}")

    def save_credentials(self):
        """Save credentials if remember option is checked"""
        try:
            if self.remember_var.get():
                self.config.set('credentials.username', self.username)
                self.config.set('credentials.password', self.password)
                self.config.set('credentials.remember', True)
            else:
                # Clear saved credentials if not remembering
                self.config.set('credentials.username', '')
                self.config.set('credentials.password', '')
                self.config.set('credentials.remember', False)

            self.config.save_config()
        except Exception as e:
            print(f"Error saving credentials: {e}")

    def handle_login(self):
        """Handle login process"""
        self.username = self.username_entry.get().strip()
        self.password = self.password_entry.get().strip()

        if not self.username or not self.password:
            self.show_status("Vui lòng nhập tên đăng nhập và mật khẩu", "error")
            return

        # Disable login button during authentication
        self.login_btn.config(state="disabled", text="Đang đăng nhập...")
        self.show_status("Đang xác thực...", "info")

        # Start login in background thread
        threading.Thread(
            target=self.login_thread,
            daemon=True
        ).start()

    def login_thread(self):
        """Login process in background thread"""
        try:
            # Attempt login
            login_success = self.api_client.get_bearer_token(self.username, self.password)

            if login_success:
                # Login successful
                self.window.after(0, self.on_login_success)
            else:
                # Login failed
                self.window.after(0, self.on_login_failure)

        except Exception as e:
            print(f"Login error: {e}")
            self.window.after(0, self.on_login_failure)

    def on_login_success(self):
        """Handle successful login"""
        self.login_successful = True

        # Save credentials if remember is checked
        self.save_credentials()

        self.show_status("Đăng nhập thành công!", "success")

        # Wait a moment then close login window and open main app
        self.window.after(1000, self.open_main_app)

    def on_login_failure(self):
        """Handle login failure"""
        self.login_btn.config(state="normal", text="Đăng nhập")
        self.show_status("Tên đăng nhập hoặc mật khẩu không chính xác", "error")
        self.password_entry.delete(0, tk.END)
        self.password_entry.focus()

    def show_status(self, message, status_type="info"):
        """Show status message"""
        colors = {
            "error": "red",
            "success": "green",
            "info": "blue"
        }
        self.status_label.config(text=message, foreground=colors.get(status_type, "black"))

    def open_main_app(self):
        """Close login window and open main application"""
        # Store the callback and API client before destroying the window
        callback = self.on_success_callback
        username = self.username
        password = self.password
        api_client = self.api_client

        # Withdraw the login window first
        self.window.withdraw()

        # Create the main app in a try-except block
        try:
            callback(username, password, api_client)
        except Exception as e:
            print(f"Error creating main app: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Destroy the login window after main app is created
            try:
                self.window.destroy()
            except:
                pass

    def run(self):
        """Start the login window"""
        self.window.mainloop()
        return self.login_successful, self.username, self.password


class DossierProcessorApp:
    """Main application class"""

    def __init__(self, username, password, authenticated_api_client):
        self.config = ConfigManager()
        self.processing = False
        self.total_codes = 0
        self.processed_codes = 0
        self.is_logged_in = True  # Already authenticated
        self.current_username = username
        self.current_password = password

        # Use the authenticated API client
        self.api_client = authenticated_api_client

        # Hardcoded database configuration
        print(f"[DEBUG] Using hardcoded database configuration:")
        print(f"[DEBUG] URI: {HARDCODED_DATABASE_URI}")
        print(f"[DEBUG] Database: {HARDCODED_DATABASE_NAME}")
        print(f"[DEBUG] Collection: {HARDCODED_COLLECTION_NAME}")

        # Initialize database manager with hardcoded values
        self.db_manager = DatabaseManager(
            HARDCODED_DATABASE_URI,
            HARDCODED_DATABASE_NAME,
            HARDCODED_COLLECTION_NAME
        )

        # Initialize UI elements as None first
        self.root = None
        self.codes_text = None
        self.process_btn = None
        self.progress_var = None
        self.progress_bar = None
        self.status_label = None
        self.results_text = None

        # Setup UI
        self.setup_ui()

        # Test database connection after UI is ready
        if self.results_text:
            self.test_database_connection()

    def setup_ui(self):
        """Initialize the user interface"""
        if BOOTSTRAP_AVAILABLE:
            self.root = ttk_bootstrap.Window(
                themename=self.config.get('ui.theme', 'superhero')
            )
        else:
            self.root = tk.Tk()

        self.root.title("Công cụ xử lý mã hồ sơ")
        self.root.geometry(self.config.get('ui.window_size', '700x500'))
        self.root.resizable(True, True)

        # Configure grid weights for responsive layout
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # Create main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        main_frame.columnconfigure(1, weight=1)

        # Configure row weights for proper layout
        main_frame.rowconfigure(3, weight=1)  # Results section should expand

        # Create user info header
        self.create_user_header(main_frame)
        self.create_input_section(main_frame)
        self.create_progress_section(main_frame)
        self.create_results_section(main_frame)

    def create_user_header(self, parent):
        """Create user info header with logout button"""
        header_frame = ttk.Frame(parent, padding="5")
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        header_frame.columnconfigure(0, weight=1)

        # Welcome message
        welcome_label = ttk.Label(
            header_frame,
            text=f"Xin chào, {self.current_username}",
            font=('Arial', 11, 'bold')
        )
        welcome_label.grid(row=0, column=0, sticky="w")

        # Logout button
        logout_btn = ttk.Button(
            header_frame,
            text="Đăng xuất",
            command=self.handle_logout,
            style="danger.TButton" if BOOTSTRAP_AVAILABLE else None
        )
        logout_btn.grid(row=0, column=1, sticky="e")

    def create_input_section(self, parent):
        """Create input section for dossier codes"""
        # Input frame
        input_frame = ttk.LabelFrame(parent, text="Mã hồ sơ", padding="5")
        input_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(1, weight=1)

        # Codes entry label
        ttk.Label(input_frame, text="Danh sách mã hồ sơ:").grid(
            row=0, column=0, sticky="w", pady=(0, 5)
        )

        # Large text area for codes instead of single line entry
        if BOOTSTRAP_AVAILABLE:
            # Use ttkbootstrap Text widget for theming
            from ttkbootstrap.scrolled import ScrolledText
            self.codes_text = ScrolledText(
                input_frame,
                height=6,
                font=('Consolas', 10),
                wrap=tk.WORD,
                autohide=True
            )
            self.codes_text.grid(row=1, column=0, columnspan=2, sticky="nsew", pady=(0, 10))
        else:
            # Fallback to regular Text with scrollbar
            self.codes_text = tk.Text(
                input_frame,
                height=6,
                font=('Consolas', 10),
                wrap=tk.WORD
            )
            self.codes_text.grid(row=1, column=0, sticky="nsew", padx=(0, 10), pady=(0, 10))

            # Add scrollbar to text area
            codes_scrollbar = ttk.Scrollbar(input_frame, orient="vertical", command=self.codes_text.yview)
            codes_scrollbar.grid(row=1, column=1, sticky="ns", pady=(0, 10))
            self.codes_text.configure(yscrollcommand=codes_scrollbar.set)

        # Bind paste event to auto-format with commas
        self.codes_text.bind('<Control-v>', self.on_paste_codes)
        # Also bind for right-click paste
        self.codes_text.bind('<Button-3>', self.show_context_menu)

        # Buttons frame
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.grid(row=2, column=0, columnspan=2, pady=(5, 0))

        # Process button (enabled since user is already logged in)
        self.process_btn = ttk.Button(
            buttons_frame,
            text="Xử lý trễ hạn",
            command=self.start_processing,
            style="success.TButton" if BOOTSTRAP_AVAILABLE else None
        )
        self.process_btn.pack(side="left", padx=(0, 10))

        # TPHS button
        self.tphs_btn = ttk.Button(
            buttons_frame,
            text="Thêm TPHS",
            command=self.start_tphs_processing,
            style="info.TButton" if BOOTSTRAP_AVAILABLE else None
        )
        self.tphs_btn.pack(side="left", padx=(0, 10))

        # Thêm file KQ button
        self.add_kq_file_btn = ttk.Button(
            buttons_frame,
            text="Thêm file KQ",
            command=self.start_add_kq_file_processing,
            style="warning.TButton" if BOOTSTRAP_AVAILABLE else None
        )
        self.add_kq_file_btn.pack(side="left")

    def create_config_section(self, parent):
        """Create configuration section"""
        config_frame = ttk.LabelFrame(parent, text="Cấu hình", padding="5")
        config_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # Database status (hardcoded, no edit)
        ttk.Label(config_frame, text="Cơ sở dữ liệu:").grid(
            row=0, column=0, sticky="w", padx=(0, 10)
        )
        db_status_label = ttk.Label(config_frame, text="103.130.221.125:27017/svcPadman")
        db_status_label.grid(row=0, column=1, sticky="w", padx=(0, 10))

        self.mongo_status = ttk.Label(config_frame, text="●", foreground="red")
        self.mongo_status.grid(row=0, column=2)

        # API Auth URL
        ttk.Label(config_frame, text="API Auth:").grid(
            row=1, column=0, sticky="w", padx=(0, 10), pady=(5, 0)
        )
        self.api_auth_entry = ttk.Entry(config_frame)
        self.api_auth_entry.grid(row=1, column=1, sticky="ew", padx=(0, 10), pady=(5, 0))
        self.api_auth_entry.insert(0, self.config.get('api.auth_endpoint', ''))

        # API Notify URL
        ttk.Label(config_frame, text="API Thông báo:").grid(
            row=2, column=0, sticky="w", padx=(0, 10), pady=(5, 0)
        )
        self.api_notify_entry = ttk.Entry(config_frame)
        self.api_notify_entry.grid(row=2, column=1, sticky="ew", padx=(0, 10), pady=(5, 0))
        self.api_notify_entry.insert(0, self.config.get('api.notify_endpoint', ''))

        self.api_status = ttk.Label(config_frame, text="●", foreground="orange")
        self.api_status.grid(row=2, column=2, pady=(5, 0))

    def create_progress_section(self, parent):
        """Create progress tracking section"""
        progress_frame = ttk.LabelFrame(parent, text="Tiến độ", padding="5")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.grid(row=0, column=0, sticky="ew", pady=(0, 5))

        # Status label
        self.status_label = ttk.Label(progress_frame, text="Sẵn sàng")
        self.status_label.grid(row=1, column=0, sticky="w")

    def create_results_section(self, parent):
        """Create results display section"""
        results_frame = ttk.LabelFrame(parent, text="Kết quả", padding="5")
        results_frame.grid(row=3, column=0, columnspan=2, sticky="nsew", pady=(0, 0))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Results text widget with scrollbar - use themed version if available
        if BOOTSTRAP_AVAILABLE:
            from ttkbootstrap.scrolled import ScrolledText
            self.results_text = ScrolledText(
                results_frame,
                height=10,
                font=('Consolas', 9),
                wrap=tk.WORD,
                autohide=True
            )
        else:
            # Fallback to regular scrolledtext
            self.results_text = scrolledtext.ScrolledText(
                results_frame,
                height=10,
                font=('Consolas', 9),
                wrap=tk.WORD
            )

        self.results_text.grid(row=0, column=0, sticky="nsew")

    def test_database_connection(self):
        """Test database connection and show status"""
        try:
            if self.db_manager.connect():
                self.log_result("✓ Kết nối cơ sở dữ liệu thành công")
            else:
                self.log_result("✗ Kết nối cơ sở dữ liệu thất bại")

            # Log user and API status
            self.log_result(f"✓ Đã đăng nhập với tài khoản: {self.current_username}")
            if self.api_client and self.api_client.token_expires_in:
                self.log_result(f"  Token hết hạn sau {self.api_client.token_expires_in} giây")
        except Exception as e:
            print(f"Error in test_database_connection: {e}")

    def test_connections(self):
        """Test database and API connections"""
        # Test database connection
        if self.db_manager.connect():
            self.log_result("✓ Kết nối cơ sở dữ liệu thành công")
        else:
            self.log_result("✗ Kết nối cơ sở dữ liệu thất bại")

        # API status (just log that it's ready)
        if REQUESTS_AVAILABLE:
            self.log_result("✓ API client sẵn sàng")
        else:
            self.log_result("✗ API client không khả dụng")

    def start_processing(self):
        """Start processing dossier codes in a separate thread"""
        if self.processing:
            return

        # Get text from the correct widget
        text_widget = self.codes_text
        if hasattr(self.codes_text, 'text'):
            # ttkbootstrap ScrolledText has a 'text' attribute
            text_widget = self.codes_text.text

        codes_text = text_widget.get("1.0", tk.END).strip()
        if not codes_text:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập mã hồ sơ")
            return

        codes = CodeProcessor.parse_dossier_codes(codes_text)
        if not codes:
            messagebox.showwarning("Cảnh báo", "Không tìm thấy mã hồ sơ hợp lệ")
            return

        # Validate codes
        invalid_codes = [code for code in codes if not CodeProcessor.validate_code(code)]
        if invalid_codes:
            result = messagebox.askyesno(
                "Mã không hợp lệ",
                f"Tìm thấy mã không hợp lệ: {', '.join(invalid_codes)}\n\nTiếp tục với các mã hợp lệ?"
            )
            if not result:
                return
            codes = [code for code in codes if CodeProcessor.validate_code(code)]

        # Start processing in background thread
        self.processing = True
        self.total_codes = len(codes)
        self.processed_codes = 0

        self.process_btn.config(state="disabled")
        self.tphs_btn.config(state="disabled")
        self.progress_var.set(0)
        self.results_text.delete(1.0, tk.END)

        threading.Thread(
            target=self.process_codes_thread,
            args=(codes,),
            daemon=True
        ).start()

    def start_tphs_processing(self):
        """Start TPHS processing from text input"""
        if self.processing:
            return

        # Get text from the correct widget
        text_widget = self.codes_text
        if hasattr(self.codes_text, 'text'):
            text_widget = self.codes_text.text

        codes_text = text_widget.get("1.0", tk.END).strip()
        if not codes_text:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập mã hồ sơ")
            return

        codes = CodeProcessor.parse_dossier_codes(codes_text)
        if not codes:
            messagebox.showwarning("Cảnh báo", "Không tìm thấy mã hồ sơ hợp lệ")
            return

        # Start TPHS processing in background thread
        self.processing = True
        self.total_codes = len(codes)
        self.processed_codes = 0

        self.process_btn.config(state="disabled")
        self.tphs_btn.config(state="disabled")
        self.add_kq_file_btn.config(state="disabled")
        self.progress_var.set(0)
        self.results_text.delete(1.0, tk.END)

        threading.Thread(
            target=self.process_tphs_thread,
            args=(codes,),
            daemon=True
        ).start()

    def start_add_kq_file_processing(self):
        """Start adding KQ file processing from text input"""
        if self.processing:
            messagebox.showwarning("Cảnh báo", "Đang xử lý, vui lòng chờ...")
            return

        # Get text from the correct widget
        text_widget = self.codes_text
        if hasattr(self.codes_text, 'text'):
            text_widget = self.codes_text.text

        codes_text = text_widget.get("1.0", tk.END).strip()
        if not codes_text:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập mã hồ sơ")
            return

        codes = CodeProcessor.parse_dossier_codes(codes_text)
        if not codes:
            messagebox.showwarning("Cảnh báo", "Không tìm thấy mã hồ sơ hợp lệ")
            return

        # Start KQ file processing in background thread
        self.processing = True
        self.total_codes = len(codes)
        self.processed_codes = 0

        self.process_btn.config(state="disabled")
        self.tphs_btn.config(state="disabled")
        self.add_kq_file_btn.config(state="disabled")
        self.progress_var.set(0)
        self.results_text.delete(1.0, tk.END)

        threading.Thread(
            target=self.process_add_kq_file_thread,
            args=(codes,),
            daemon=True
        ).start()

    def process_codes_thread(self, codes: List[str]):
        """Process codes in background thread"""
        print(f"[DEBUG] Starting process thread with {len(codes)} codes")
        self.update_status(f"Đang xử lý {len(codes)} mã...")
        self.log_result(f"Bắt đầu xử lý {len(codes)} mã hồ sơ")

        # Use current user credentials for token refresh if needed
        username = self.current_username
        password = self.current_password
        print(f"[DEBUG] Using credentials for user: {username}")

        # Reconnect with hardcoded database settings
        print("[DEBUG] Reconnecting to database...")
        self.db_manager.disconnect()
        connected = self.db_manager.connect()

        if not connected:
            print("[DEBUG] Database connection failed, aborting processing")
            self.log_result("✗ Không thể kết nối cơ sở dữ liệu")
            self.finish_processing()
            return

        print("[DEBUG] Database connected successfully")

        # Update API client endpoints with hardcoded values
        self.api_client.auth_url = HARDCODED_AUTH_ENDPOINT
        self.api_client.notify_url = HARDCODED_NOTIFY_ENDPOINT
        print(f"[DEBUG] Using hardcoded API endpoints:")
        print(f"[DEBUG] Auth: {HARDCODED_AUTH_ENDPOINT}")
        print(f"[DEBUG] Notify: {HARDCODED_NOTIFY_ENDPOINT}")

        # Check if we have a valid token, log status
        if self.api_client.is_token_valid():
            self.log_result("✓ Sử dụng token đã lưu")
            if self.api_client.token_expires_in:
                elapsed = (datetime.now() - self.api_client.token_acquired_at).total_seconds()
                remaining = self.api_client.token_expires_in - elapsed
                self.log_result(f"  Token còn hạn {int(remaining)} giây")
                print(f"[DEBUG] Token valid, {int(remaining)} seconds remaining")
        else:
            print("[DEBUG] No valid token available")
            self.log_result("✗ Không có token hợp lệ - cần đăng nhập lại")

        success_count = 0
        not_found_count = 0
        error_count = 0

        print(f"[DEBUG] Beginning to process {len(codes)} codes...")
        for i, code in enumerate(codes):
            print(f"[DEBUG] Processing code {i+1}/{len(codes)}: {code}")
            self.update_status(f"Đang xử lý {code} ({i+1}/{len(codes)})")

            # Process the code
            result = self.db_manager.sync_ho_so_tre_han(code)
            print(f"[DEBUG] Database processing result for {code}: {result}")

            if result == 1:
                # Success - notify API with persistent token
                print(f"[DEBUG] Database update successful for {code}, sending API notification...")
                api_success = self.api_client.notify_success(code)
                if api_success:
                    api_status = " (API ✓ - đã đồng bộ)"
                    print(f"[DEBUG] API notification successful for {code}")
                else:
                    api_status = " (API ✗ - lỗi đồng bộ hoặc token hết hạn)"
                    print(f"[DEBUG] API notification failed for {code}")
                self.log_result(f"✓ {code} - Cập nhật thành công{api_status}")
                success_count += 1
            elif result == -1:
                print(f"[DEBUG] Document not found for {code}")
                self.log_result(f"✗ {code} - Không tìm thấy hồ sơ")
                not_found_count += 1
            else:
                print(f"[DEBUG] Error processing {code}")
                self.log_result(f"✗ {code} - Lỗi xử lý")
                error_count += 1

            # Update progress
            self.processed_codes += 1
            progress = (self.processed_codes / self.total_codes) * 100
            self.root.after(0, lambda p=progress: self.progress_var.set(p))
            print(f"[DEBUG] Progress: {progress:.1f}% ({self.processed_codes}/{self.total_codes})")

        # Summary
        print(f"[DEBUG] Processing complete - Success: {success_count}, Not found: {not_found_count}, Errors: {error_count}")
        self.log_result("\n" + "="*50)
        self.log_result(f"Xử lý hoàn tất:")
        self.log_result(f"  Thành công: {success_count}")
        self.log_result(f"  Không tìm thấy: {not_found_count}")
        self.log_result(f"  Lỗi: {error_count}")
        self.log_result(f"  Tổng cộng: {len(codes)}")

        self.finish_processing()

    def update_status(self, message: str):
        """Update status label from any thread"""
        self.root.after(0, lambda: self.status_label.config(text=message))

    def log_result(self, message: str):
        """Add message to results text from any thread"""
        def _log():
            try:
                if self.results_text:
                    # Handle both ttkbootstrap ScrolledText and regular scrolledtext
                    if hasattr(self.results_text, 'text'):
                        # ttkbootstrap ScrolledText has a 'text' attribute
                        text_widget = self.results_text.text
                    else:
                        # Regular scrolledtext.ScrolledText
                        text_widget = self.results_text

                    text_widget.insert(tk.END, message + "\n")
                    text_widget.see(tk.END)
                else:
                    print(f"Log: {message}")
            except Exception as e:
                print(f"Error logging result: {e} - Message: {message}")

        try:
            if self.root:
                self.root.after(0, _log)
            else:
                print(f"Log: {message}")
        except Exception as e:
            print(f"Error scheduling log: {e} - Message: {message}")

    def update_status(self, message: str):
        """Update status label from background thread"""
        def _update():
            if self.status_label:
                self.status_label.config(text=message)

        self.root.after(0, _update)

    def update_progress(self, progress: float):
        """Update progress bar from background thread"""
        def _update():
            if self.progress_var:
                self.progress_var.set(progress)

        self.root.after(0, _update)

    def finish_processing(self):
        """Clean up after processing is complete"""
        def _finish():
            self.processing = False
            self.process_btn.config(state="normal")
            self.tphs_btn.config(state="normal")
            self.add_kq_file_btn.config(state="normal")
            self.update_status("Xử lý hoàn tất")

        self.root.after(0, _finish)

    def process_tphs_thread(self, codes: List[str]):
        """Process TPHS codes in background thread"""
        print(f"[DEBUG] Starting TPHS process thread with {len(codes)} codes")
        self.update_status(f"Đang xử lý TPHS {len(codes)} mã...")
        self.log_result(f"Bắt đầu xử lý TPHS {len(codes)} mã hồ sơ")

        # Reconnect to database
        print("[DEBUG] Reconnecting to database for TPHS processing...")
        self.db_manager.disconnect()
        connected = self.db_manager.connect()

        if not connected:
            print("[DEBUG] Database connection failed, aborting TPHS processing")
            self.log_result("✗ Không thể kết nối cơ sở dữ liệu")
            self.finish_processing()
            return

        print("[DEBUG] Database connected successfully for TPHS")

        # Get dossier IDs from codes
        self.log_result("Đang tìm kiếm dossier IDs...")
        dossier_map = self.db_manager.find_dossier_ids(codes)

        # Get template document for cloning
        self.log_result("Đang tìm kiếm template document...")
        template_doc = self.db_manager.get_template_document()
        if not template_doc:
            self.log_result("⚠ Không tìm thấy template document để clone")

        # Process results tracking
        results = {
            'total_codes': len(codes),
            'skipped': 0,
            'successful_updates': 0,
            'successful_clones': 0,
            'failed_operations': 0,
            'not_found_codes': [],
            'failed_details': [],
            'skipped_codes': []
        }

        for i, code in enumerate(codes, 1):
            if not self.processing:  # Check if processing was stopped
                break

            print(f"[DEBUG] Processing TPHS code {i}/{len(codes)}: {code}")
            self.update_status(f"Đang xử lý TPHS {i}/{len(codes)}: {code}")

            if code in dossier_map:
                dossier_id = dossier_map[code]
                update_result = self.db_manager.update_tphs_file(dossier_id, template_doc)

                if update_result['success']:
                    if update_result['action'] == 'update':
                        results['successful_updates'] += 1
                        self.log_result(f"✓ {code}: Cập nhật thành công ({update_result['message']})")
                    elif update_result['action'] == 'clone':
                        results['successful_clones'] += 1
                        self.log_result(f"✓ {code}: Tạo mới từ template thành công")
                    elif update_result['action'] == 'skip':
                        results['skipped'] += 1
                        results['skipped_codes'].append(code)
                        self.log_result(f"⏭ {code}: Đã có file reused=1")
                else:
                    results['failed_operations'] += 1
                    results['failed_details'].append({
                        'code': code,
                        'error': update_result['message']
                    })
                    self.log_result(f"✗ {code}: Lỗi - {update_result['message']}")
            else:
                results['not_found_codes'].append(code)
                self.log_result(f"✗ {code}: Không tìm thấy trong cơ sở dữ liệu")

            # Update progress
            self.processed_codes = i
            progress = (i / len(codes)) * 100
            self.update_progress(progress)

        # Final TPHS summary
        self.log_result("\n" + "="*60)
        self.log_result("KẾT QUẢ XỬ LÝ TPHS")
        self.log_result("="*60)
        self.log_result(f"📊 Tổng số mã xử lý: {results['total_codes']}")
        self.log_result(f"⏭️  Bỏ qua (đã có reused=1): {results['skipped']}")
        self.log_result(f"✅ Cập nhật thành công: {results['successful_updates']}")
        self.log_result(f"🔄 Tạo mới thành công: {results['successful_clones']}")
        self.log_result(f"❌ Thất bại: {results['failed_operations']}")
        self.log_result(f"🔍 Không tìm thấy: {len(results['not_found_codes'])}")

        if results['skipped_codes']:
            self.log_result(f"\n⏭️  MÃ ĐÃ BỎ QUA: {', '.join(results['skipped_codes'])}")

        if results['not_found_codes']:
            self.log_result(f"\n🔍 MÃ KHÔNG TÌM THẤY: {', '.join(results['not_found_codes'])}")

        if results['failed_details']:
            self.log_result(f"\n❌ CHI TIẾT LỖI:")
            for detail in results['failed_details']:
                self.log_result(f"   • {detail['code']}: {detail['error']}")

        self.finish_processing()

    def process_add_kq_file_thread(self, codes: List[str]):
        """Process adding KQ files in background thread"""
        print(f"[DEBUG] Starting KQ file processing thread with {len(codes)} codes")
        self.update_status(f"Đang thêm file KQ cho {len(codes)} mã...")
        self.log_result(f"Bắt đầu thêm file KQ cho {len(codes)} mã hồ sơ")

        # Use current user credentials for token refresh if needed
        username = self.current_username
        password = self.current_password
        print(f"[DEBUG] Using credentials for user: {username}")

        # Check if we have a valid token, refresh if needed
        if not self.api_client.is_token_valid():
            self.log_result("Token hết hạn, đang làm mới...")
            if not self.api_client.get_bearer_token(username, password):
                self.log_result("✗ Không thể làm mới token")
                self.finish_processing()
                return
            self.log_result("✓ Token đã được làm mới")

        # Process each code
        successful_codes = []
        failed_codes = []

        for i, code in enumerate(codes, 1):
            try:
                self.update_status(f"Đang xử lý {code} ({i}/{len(codes)})")
                print(f"[DEBUG] Processing KQ file for code: {code}")

                # Fetch dossier details and update file
                success = self.fetch_dossier_details_and_update_file(code)

                if success:
                    successful_codes.append(code)
                    self.log_result(f"✓ {code}: Thêm file KQ thành công")
                else:
                    failed_codes.append(code)
                    self.log_result(f"✗ {code}: Không thể thêm file KQ")

            except Exception as e:
                failed_codes.append(code)
                self.log_result(f"✗ {code}: Lỗi - {str(e)}")
                print(f"[DEBUG] Error processing {code}: {e}")

            # Update progress
            self.processed_codes = i
            progress = (i / len(codes)) * 100
            self.update_progress(progress)

        # Log summary
        self.log_result(f"\n=== KẾT QUẢ THÊM FILE KQ ===")
        self.log_result(f"Thành công: {len(successful_codes)}")
        self.log_result(f"Thất bại: {len(failed_codes)}")

        if failed_codes:
            self.log_result(f"Mã thất bại: {', '.join(failed_codes[:10])}")
            if len(failed_codes) > 10:
                self.log_result(f"... và {len(failed_codes) - 10} mã khác")

        self.finish_processing()

    def fetch_dossier_details_and_update_file(self, dossier_code: str) -> bool:
        """Fetch dossier details and update file attachments (KQ file functionality)"""
        try:
            print(f"[DEBUG] Starting KQ file process for dossier: {dossier_code}")

            # Step 1: Fetch dossier by code
            dossier_result = self.api_client.fetch_dossier_by_code(dossier_code)
            if not dossier_result['success']:
                print(f"[DEBUG] Failed to fetch dossier by code: {dossier_result['error']}")
                return False

            dossier_data = dossier_result['data']
            dossier_id = dossier_data.get('id')
            if not dossier_id:
                print(f"[DEBUG] No dossier ID found for {dossier_code}")
                return False

            print(f"[DEBUG] Found dossier ID: {dossier_id}")

            # Get dates for logging
            accepted_date = dossier_data.get('acceptedDate')
            appointment_date = dossier_data.get('appointmentDate')
            completed_date = dossier_data.get('completedDate')
            print(f"[DEBUG] Dossier dates - Accepted: {accepted_date}, Appointment: {appointment_date}, Completed: {completed_date}")

            # Step 2: Fetch detailed dossier information
            detail_result = self.api_client.fetch_dossier_details(dossier_id)
            if not detail_result['success']:
                print(f"[DEBUG] Failed to fetch dossier details: {detail_result['error']}")
                return False

            detail_data = detail_result['data']
            print(f"[DEBUG] detail_data keys: {list(detail_data.keys()) if isinstance(detail_data, dict) else 'Not a dict'}")
            print(f"[DEBUG] detail_data type: {type(detail_data)}")

            # Try different possible keys for dossier form files
            dossier_form_files = detail_data.get('dossierFormFile', [])
            print(f"[DEBUG] dossierFormFile value: {dossier_form_files}")
            print(f"[DEBUG] dossierFormFile type: {type(dossier_form_files)}")
            print(f"[DEBUG] dossierFormFile length: {len(dossier_form_files) if isinstance(dossier_form_files, (list, tuple)) else 'N/A'}")

            if not dossier_form_files:
                print(f"[DEBUG] No dossier form files found for {dossier_code}")
                print(f"[DEBUG] Available keys in detail_data: {list(detail_data.keys()) if isinstance(detail_data, dict) else 'N/A'}")
                # Print sample of data structure for debugging
                if isinstance(detail_data, dict):
                    for k, v in list(detail_data.items())[:3]:
                        print(f"[DEBUG] Key '{k}': type={type(v)}, value preview={str(v)[:200] if v else 'None/Empty'}")
                return False

            # Step 3: Extract file information from dossier form files
            print(f"[DEBUG] Examining {len(dossier_form_files)} dossier form files")

            # Look through all dossier form files to find one with files
            file_found = False
            file_id = None
            file_size = 0

            for i, form_file in enumerate(dossier_form_files):
                print(f"[DEBUG] Form file {i}: keys = {list(form_file.keys()) if isinstance(form_file, dict) else 'Not a dict'}")

                # Try different possible keys for files
                files = None
                possible_file_keys = ['file', 'files', 'attachments', 'documents']

                for key in possible_file_keys:
                    if key in form_file and form_file[key]:
                        files = form_file[key]
                        print(f"[DEBUG] Found files under key '{key}' in form file {i}: {len(files) if isinstance(files, list) else 'Not a list'}")
                        break

                if files and isinstance(files, list) and len(files) > 0:
                    first_file = files[0]
                    print(f"[DEBUG] First file structure: {first_file}")

                    file_id = first_file.get('id')
                    file_size = first_file.get('size', 0)

                    if file_id:
                        print(f"[DEBUG] Found file - ID: {file_id}, Size: {file_size}")
                        file_found = True
                        break
                else:
                    print(f"[DEBUG] No files in form file {i}, structure: {form_file}")

            if not file_found or not file_id:
                print(f"[DEBUG] No files with valid ID found in any dossier form file for {dossier_code}")
                return False

            print(f"[DEBUG] Using file - ID: {file_id}, Size: {file_size}")

            # Step 4: Create attachment data
            filename = f"{dossier_code}-KQ.pdf"
            attachment = [{
                "id": file_id,
                "filename": filename,
                "size": file_size,
                "group": "5f9bd9692994dc687e68b5a6"
            }]

            print(f"[DEBUG] Created attachment: {attachment}")

            # Step 5: Update attachments
            update_result = self.api_client.update_dossier_attachments(dossier_id, attachment)
            if not update_result['success']:
                print(f"[DEBUG] Failed to update attachments: {update_result['error']}")
                return False

            print(f"[DEBUG] Successfully updated attachments for {dossier_code}")
            return True

        except Exception as e:
            print(f"[DEBUG] Error in fetch_dossier_details_and_update_file for {dossier_code}: {e}")
            import traceback
            print(f"[DEBUG] Full traceback: {traceback.format_exc()}")
            return False

    def on_paste_codes(self, event):
        """Handle paste event to auto-format codes with commas"""
        try:
            # Get clipboard content
            clipboard_content = self.root.clipboard_get()
            if not clipboard_content:
                return

            # Parse and format the codes
            formatted_codes = self.format_pasted_codes(clipboard_content)

            # Get the correct text widget
            text_widget = self.codes_text
            if hasattr(self.codes_text, 'text'):
                # ttkbootstrap ScrolledText has a 'text' attribute
                text_widget = self.codes_text.text

            # Insert formatted codes at cursor position
            cursor_pos = text_widget.index(tk.INSERT)
            text_widget.insert(cursor_pos, formatted_codes)

            # Prevent default paste behavior
            return "break"
        except tk.TclError:
            # No clipboard content or error accessing clipboard
            pass
        except Exception as e:
            print(f"Error in paste handler: {e}")

    def format_pasted_codes(self, text: str) -> str:
        """Format pasted text to have comma-separated codes"""
        # Remove extra whitespace and split by various delimiters
        codes = re.split(r'[\s\n\r\t]+', text.strip())
        # Filter out empty strings
        codes = [code.strip() for code in codes if code.strip()]
        # Join with commas and space
        return ', '.join(codes)

    def show_context_menu(self, event):
        """Show context menu with paste option"""
        try:
            # Get the correct text widget
            text_widget = self.codes_text
            if hasattr(self.codes_text, 'text'):
                text_widget = self.codes_text.text

            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="Dán và định dạng", command=self.paste_and_format)
            context_menu.add_separator()
            context_menu.add_command(label="Dán thường", command=lambda: text_widget.event_generate('<<Paste>>'))
            context_menu.add_command(label="Sao chép", command=lambda: text_widget.event_generate('<<Copy>>'))
            context_menu.add_command(label="Cắt", command=lambda: text_widget.event_generate('<<Cut>>'))
            context_menu.add_separator()
            context_menu.add_command(label="Chọn tất cả", command=lambda: text_widget.tag_add(tk.SEL, "1.0", tk.END))
            context_menu.add_command(label="Xóa tất cả", command=lambda: text_widget.delete("1.0", tk.END))

            # Show context menu
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            print(f"Error showing context menu: {e}")
        finally:
            if 'context_menu' in locals():
                context_menu.grab_release()

    def paste_and_format(self):
        """Paste and format codes from context menu"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                formatted_codes = self.format_pasted_codes(clipboard_content)

                # Get the correct text widget
                text_widget = self.codes_text
                if hasattr(self.codes_text, 'text'):
                    text_widget = self.codes_text.text

                cursor_pos = text_widget.index(tk.INSERT)
                text_widget.insert(cursor_pos, formatted_codes)
        except tk.TclError:
            pass
        except Exception as e:
            print(f"Error in paste and format: {e}")
    def handle_logout(self):
        """Handle logout process - close main app and return to login"""
        result = messagebox.askyesno(
            "Xác nhận đăng xuất",
            "Bạn có chắc chắn muốn đăng xuất không?"
        )

        if result:
            # Clear API token from session and config
            if self.api_client:
                self.api_client.clear_saved_token()
                self.api_client.access_token = None
                self.api_client.token_expires_in = None
                self.api_client.token_acquired_at = None
                if self.api_client.session:
                    self.api_client.session.headers.pop('Authorization', None)

            # Close database connection
            if self.db_manager:
                self.db_manager.disconnect()

            # Close main window and restart login
            self.root.destroy()

            # Restart the application from login
            restart_app()

    def run(self):
        """Start the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Handle application shutdown"""
        if self.db_manager:
            self.db_manager.disconnect()
        self.root.destroy()


def restart_app():
    """Restart the application from login"""
    config = ConfigManager()

    def on_login_success(username, password, api_client):
        """Callback when login is successful"""
        try:
            app = DossierProcessorApp(username, password, api_client)
            app.run()
        except Exception as e:
            print(f"Application error: {e}")
            import traceback
            traceback.print_exc()

    # Show login window
    login_window = LoginWindow(config, on_login_success)
    login_window.run()


def main():
    """Main entry point"""
    restart_app()


if __name__ == "__main__":
    main()