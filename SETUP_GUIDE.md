# HƯỚNG DẪN THIẾT LẬP MÔI TRƯỜNG RUST + TAURI

## Tình trạng hiện tại
- ✅ Mã nguồn Rust đã hoàn thiện (100% tính năng từ Python)
- ✅ Frontend HTML sẵn sàng
- ❌ Môi trường build Windows chưa đầy đủ

## Vấn đề gặp phải
Lỗi: `link.exe: extra operand` - Thiếu Visual Studio Build Tools

## GIẢI PHÁP 1: CÀI ĐẶT VISUAL STUDIO BUILD TOOLS (KHUYẾN NGHỊ)

### Bước 1: Tải về Visual Studio Build Tools
1. Truy cập: https://visualstudio.microsoft.com/downloads/
2. Cuộn xuống phần "All Downloads"
3. Tải "Build Tools for Visual Studio 2022"

### Bước 2: Cài đặt
1. Chạy file đã tải về
2. Chọn "C++ build tools" workload
3. Đảm bảo các component được chọn:
   - MSVC v143 - VS 2022 C++ x64/x86 build tools
   - Windows 10/11 SDK (phi<PERSON><PERSON> bản mới nhất)
   - CMake tools for Visual Studio
4. Nhấn "Install"

### Bước 3: Khởi động lại
1. Khởi động lại máy tính
2. Mở Command Prompt mới
3. Chạy lệnh test:
```cmd
cd tool-igate-tauri\src-tauri
cargo build
```

## GIẢI PHÁP 2: SỬ DỤNG PYTHON VERSION (NGAY LẬP TỨC)

Để sử dụng ngay:
```cmd
# Chạy phiên bản Python gốc
run_tool.bat

# Hoặc trực tiếp:
python tool.py
```

## GIẢI PHÁP 3: CLOUD BUILD (GITHUB ACTIONS)

Nếu không muốn cài Visual Studio:

### Tạo file .github/workflows/build.yml:
```yaml
name: Build Tauri App

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        
    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Install Tauri CLI
      run: cargo install tauri-cli
      
    - name: Build App
      run: |
        cd tool-igate-tauri
        cargo tauri build
        
    - name: Upload Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: tauri-app
        path: tool-igate-tauri/src-tauri/target/release/bundle/
```

## GIẢI PHÁP 4: DOCKER BUILD

```dockerfile
FROM rust:1.70-windowsservercore

# Cài đặt Visual Studio Build Tools
RUN Invoke-WebRequest -Uri "https://aka.ms/vs/17/release/vs_buildtools.exe" -OutFile "vs_buildtools.exe"
RUN Start-Process -FilePath "vs_buildtools.exe" -ArgumentList "--quiet", "--wait", "--add", "Microsoft.VisualStudio.Workload.VCTools" -PassThru

# Build ứng dụng
WORKDIR /app
COPY . .
RUN cargo build --release
```

## KIỂM TRA TRẠNG THÁI

### Test môi trường Rust:
```cmd
rustc --version
cargo --version
```

### Test build:
```cmd
cd tool-igate-tauri\src-tauri
cargo check
```

### Test linker:
```cmd
where link.exe
```

## CHẠY ỨNG DỤNG SAU KHI THIẾT LẬP

```cmd
cd tool-igate-tauri
cargo tauri dev
```

Hoặc build production:
```cmd
cargo tauri build
```

## LƯU Ý QUAN TRỌNG

1. **Cần restart terminal** sau khi cài Visual Studio Build Tools
2. **Đảm bảo Windows SDK** được cài đặt
3. **Kiểm tra PATH** có chứa đường dẫn đến build tools
4. Nếu vẫn lỗi, thử cài **Visual Studio Community** (bao gồm đầy đủ tools)

## LIÊN HỆ HỖ TRỢ

Nếu gặp vấn đề, cung cấp thông tin:
- Output của `rustc --version`
- Output của `where link.exe`
- Thông tin Windows version
- Chi tiết lỗi đầy đủ