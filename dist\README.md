# Dossier Processing Tool - Executable Version

## 📋 Mô tả (Description)
Công cụ xử lý mã hồ sơ - Ứng dụng Windows độc lập để xử lý các mã hồ sơ và đồng bộ với cơ sở dữ liệu MongoDB.

Dossier Processing Tool - Standalone Windows application for processing dossier codes and synchronizing with MongoDB database.

## 🚀 Hướng dẫn sử dụng (Usage Instructions)

### Yêu cầu hệ thống (System Requirements)
- Windows 10/11 (64-bit)
- Kết nối Internet để truy cập API và cơ sở dữ liệu
- Không cần cài đặt Python hoặc các thư viện khác

### Cách chạy ứng dụng (How to Run)

1. **Giải nén file** (nếu có):
   - <PERSON><PERSON><PERSON> bả<PERSON> cả `DossierProcessorTool.exe` và `config.json` cùng trong một thư mục

2. **Chạy ứng dụng**:
   - Double-click vào `DossierProcessorTool.exe`
   - Hoặc mở Command Prompt và chạy: `DossierProcessorTool.exe`

3. **Đăng nhập**:
   - Nhập tên đăng nhập và mật khẩu
   - Chọn "Ghi nhớ tài khoản" nếu muốn lưu thông tin đăng nhập
   - Click "Đăng nhập"

4. **Xử lý mã hồ sơ**:
   - Nhập các mã hồ sơ vào ô text (cách nhau bởi dấu phẩy, space hoặc xuống dòng)
   - Ví dụ: `H12.113-250818-0004, ABC-123, DEF.456-789`
   - Click "Xử lý" để bắt đầu

## 📁 Cấu trúc file (File Structure)
```
📁 DossierProcessorTool/
├── 📄 DossierProcessorTool.exe  # Ứng dụng chính
├── 📄 config.json               # File cấu hình
└── 📄 README.md                 # Hướng dẫn này
```

## ⚙️ Cấu hình (Configuration)

File `config.json` chứa:
- **Database**: Thông tin kết nối MongoDB
- **API**: Endpoint xác thực và thông báo
- **UI**: Giao diện và theme
- **Credentials**: Thông tin đăng nhập (được mã hóa)

### Cấu hình mặc định:
```json
{
  "database": {
    "uri": "********************************************************************************************************************************",
    "database_name": "svcPadman", 
    "collection_name": "dossier"
  },
  "api": {
    "auth_endpoint": "https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token",
    "notify_endpoint": "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
  }
}
```

## 🐛 Xử lý lỗi (Troubleshooting)

### Lỗi thường gặp:

1. **"Không thể kết nối cơ sở dữ liệu"**
   - Kiểm tra kết nối Internet
   - Đảm bảo VPN hoặc network có thể truy cập MongoDB server

2. **"Đăng nhập thất bại"**
   - Kiểm tra tên đăng nhập và mật khẩu
   - Đảm bảo có kết nối đến server xác thực

3. **"Mã không hợp lệ"**
   - Mã hồ sơ chỉ được chứa: chữ cái, số, dấu gạch ngang (-), gạch dưới (_), và dấu chấm (.)
   - Độ dài tối thiểu 3 ký tự
   - Ví dụ hợp lệ: `H12.113-250818-0004`, `ABC-123`, `DEF_456`

4. **Ứng dụng không khởi động**
   - Đảm bảo có file `config.json` trong cùng thư mục
   - Chạy từ Command Prompt để xem thông báo lỗi chi tiết

## 📊 Tính năng (Features)

✅ **Giao diện tiếng Việt** - Vietnamese interface
✅ **Đăng nhập bảo mật** - Secure authentication  
✅ **Xử lý batch** - Batch processing of multiple codes
✅ **Theo dõi tiến độ** - Real-time progress tracking
✅ **Đồng bộ API** - Automatic API synchronization
✅ **Lưu token** - Token persistence for better performance
✅ **Debug logging** - Detailed logging for troubleshooting
✅ **Modern UI** - ttkbootstrap themed interface
✅ **Auto-formatting** - Automatic code formatting when pasting

## 📞 Hỗ trợ (Support)

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra phần "Xử lý lỗi" ở trên
2. Chạy ứng dụng từ Command Prompt để xem log chi tiết
3. Liên hệ với team phát triển với thông tin lỗi cụ thể

---
**Phiên bản**: 1.0
**Ngày build**: 2025-08-28
**Hệ điều hành**: Windows 10/11 (64-bit)