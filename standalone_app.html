<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C<PERSON>ng cụ xử lý mã hồ sơ - iGate V4 (Standalone)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2563eb;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #2563eb;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
        }
        .error {
            background-color: #fef2f2;
            border-color: #f87171;
            color: #dc2626;
        }
        .success {
            background-color: #f0fdf4;
            border-color: #22c55e;
            color: #16a34a;
        }
        .info {
            background-color: #f0f9ff;
            border-color: #0ea5e9;
            color: #0369a1;
        }
        .warning {
            background-color: #fefce8;
            border-color: #eab308;
            color: #a16207;
        }
        .hidden {
            display: none;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f3f4f6;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #2563eb;
            width: 0%;
            transition: width 0.3s ease;
        }
        #results {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .mode-selector {
            background-color: #e0f2fe;
            border: 1px solid #0ea5e9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .mode-selector h3 {
            margin: 0 0 10px 0;
            color: #0369a1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="mode-selector">
            <h3>🔧 Chế độ Demo (Standalone)</h3>
            <p>Đây là phiên bản demo hoạt động độc lập. Để sử dụng đầy đủ tính năng với cơ sở dữ liệu, vui lòng chọn:</p>
            <button onclick="runPythonVersion()" style="background-color: #16a34a;">Chạy phiên bản Python</button>
            <button onclick="showTauriStatus()" style="background-color: #0ea5e9;">Kiểm tra Tauri</button>
        </div>

        <!-- Login Form -->
        <div id="loginForm">
            <h1 class="header">ĐĂNG NHẬP HỆ THỐNG</h1>
            <div class="form-group">
                <label for="username">Tên đăng nhập:</label>
                <input type="text" id="username" placeholder="Nhập tên đăng nhập" value="demo">
            </div>
            <div class="form-group">
                <label for="password">Mật khẩu:</label>
                <input type="password" id="password" placeholder="Nhập mật khẩu" value="demo">
            </div>
            <div class="form-group">
                <input type="checkbox" id="remember"> 
                <label for="remember" style="display: inline; margin-left: 5px;">Ghi nhớ tài khoản</label>
            </div>
            <button onclick="login()" id="loginBtn">Đăng nhập (Demo)</button>
            <div id="loginStatus" class="status hidden"></div>
        </div>

        <!-- Main Application -->
        <div id="mainApp" class="hidden">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h1 class="header" style="margin: 0;">Công cụ xử lý mã hồ sơ</h1>
                <div>
                    <span>Xin chào, <strong id="currentUser"></strong></span>
                    <button onclick="logout()" style="margin-left: 10px; background-color: #dc2626;">Đăng xuất</button>
                </div>
            </div>

            <div class="status warning">
                <strong>⚠️ Chế độ Demo:</strong> Đây là phiên bản demo. Dữ liệu được mô phỏng và không kết nối với cơ sở dữ liệu thực.
            </div>

            <div class="form-group">
                <label for="dossierCodes">Danh sách mã hồ sơ:</label>
                <textarea id="dossierCodes" placeholder="Nhập hoặc dán danh sách mã hồ sơ (cách nhau bằng dấu phẩy, dấu cách hoặc xuống dòng)">TEST001,TEST002,TEST003</textarea>
            </div>

            <button onclick="processCodes()" id="processBtn">Xử lý (Demo)</button>
            <button onclick="testConnection()" id="testBtn">Kiểm tra kết nối (Demo)</button>

            <div class="form-group">
                <label>Tiến độ:</label>
                <div class="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <div id="statusText">Sẵn sàng</div>
            </div>

            <div class="form-group">
                <label>Kết quả:</label>
                <div id="results">Chưa có kết quả...</div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let isLoggedIn = false;
        let currentUsername = '';

        // Utility functions
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.classList.remove('hidden');
        }

        function hideStatus(elementId) {
            document.getElementById(elementId).classList.add('hidden');
        }

        function updateProgress(percentage, text) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('statusText').textContent = text;
        }

        function addResult(text) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString('vi-VN');
            results.textContent += `[${timestamp}] ${text}\n`;
            results.scrollTop = results.scrollHeight;
        }

        // Demo login function
        async function login() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                showStatus('loginStatus', 'Vui lòng nhập đầy đủ thông tin!', 'error');
                return;
            }

            showStatus('loginStatus', 'Đang đăng nhập...', 'info');
            document.getElementById('loginBtn').disabled = true;

            // Simulate login delay
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Demo login - accept any credentials
            if (username && password) {
                isLoggedIn = true;
                currentUsername = username;
                document.getElementById('currentUser').textContent = currentUsername;
                document.getElementById('loginForm').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                addResult(`✅ Đăng nhập thành công với tài khoản: ${username}`);
            } else {
                showStatus('loginStatus', 'Đăng nhập thất bại!', 'error');
            }

            document.getElementById('loginBtn').disabled = false;
        }

        // Demo logout function
        function logout() {
            isLoggedIn = false;
            currentUsername = '';
            document.getElementById('loginForm').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            hideStatus('loginStatus');
            document.getElementById('results').textContent = 'Chưa có kết quả...';
            updateProgress(0, 'Sẵn sàng');
        }

        // Demo test connection
        async function testConnection() {
            addResult('🔄 Kiểm tra kết nối...');
            document.getElementById('testBtn').disabled = true;

            await new Promise(resolve => setTimeout(resolve, 1000));

            addResult('📋 Demo Mode - Các thành phần:');
            addResult('  • Database: Mô phỏng (Demo)');
            addResult('  • API Auth: Mô phỏng (Demo)');
            addResult('  • JWT Token: Mô phỏng (Demo)');
            addResult('✅ Kết nối demo thành công!');

            document.getElementById('testBtn').disabled = false;
        }

        // Demo process codes
        async function processCodes() {
            const codesText = document.getElementById('dossierCodes').value.trim();
            if (!codesText) {
                addResult('❌ Vui lòng nhập danh sách mã hồ sơ!');
                return;
            }

            // Parse codes
            const codes = codesText
                .split(/[,\s\n]+/)
                .map(code => code.trim())
                .filter(code => code.length > 0);

            if (codes.length === 0) {
                addResult('❌ Không tìm thấy mã hồ sơ hợp lệ!');
                return;
            }

            addResult(`🚀 Bắt đầu xử lý ${codes.length} mã hồ sơ...`);
            document.getElementById('processBtn').disabled = true;

            let processed = 0;
            let successful = 0;
            let failed = 0;

            for (const code of codes) {
                updateProgress((processed / codes.length) * 100, `Đang xử lý: ${code}`);
                
                // Simulate processing delay
                await new Promise(resolve => setTimeout(resolve, 500));

                // Simulate random success/failure for demo
                const isSuccess = Math.random() > 0.2; // 80% success rate

                if (isSuccess) {
                    addResult(`✅ ${code}: Xử lý thành công`);
                    successful++;
                } else {
                    addResult(`❌ ${code}: Lỗi mô phỏng (demo)`);
                    failed++;
                }

                processed++;
            }

            updateProgress(100, 'Hoàn thành');
            addResult(`\n📊 Tổng kết:`);
            addResult(`  • Tổng số: ${codes.length}`);
            addResult(`  • Thành công: ${successful}`);
            addResult(`  • Thất bại: ${failed}`);
            addResult(`  • Tỷ lệ thành công: ${((successful / codes.length) * 100).toFixed(1)}%`);

            document.getElementById('processBtn').disabled = false;
        }

        // Helper functions for mode switching
        function runPythonVersion() {
            addResult('💡 Để chạy phiên bản Python đầy đủ:');
            addResult('1. Mở Command Prompt');
            addResult('2. Chuyển đến thư mục: e:\\Project\\tool-igate-v4');
            addResult('3. Chạy lệnh: python tool.py');
            addResult('   Hoặc double-click file: run_tool.bat');
        }

        function showTauriStatus() {
            addResult('🔧 Trạng thái Tauri:');
            if (window.__TAURI__) {
                addResult('✅ Tauri API có sẵn');
                addResult('✅ Chế độ desktop được hỗ trợ');
            } else {
                addResult('❌ Tauri API không có sẵn');
                addResult('💡 Đang cài đặt Tauri CLI...');
                addResult('💡 Sau khi cài đặt xong, chạy: cargo tauri dev');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus username field
            document.getElementById('username').focus();
            
            // Add enter key support for login
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !isLoggedIn) {
                    login();
                }
            });

            addResult('🎯 Demo mode đã sẵn sàng!');
            addResult('📝 Tài khoản demo: username="demo", password="demo"');
        });
    </script>
</body>
</html>